//+------------------------------------------------------------------+
//|                                                   Supertrend.mq5 |
//|                                    Copyright 2025, InstaScan Dev |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, InstaScan Dev"
#property link      ""
#property version   "1.00"
#property description "Supertrend Indicator for MT5"
#property indicator_chart_window
#property indicator_buffers 3
#property indicator_plots   1

//--- plot Supertrend
#property indicator_label1  "Supertrend"
#property indicator_type1   DRAW_COLOR_LINE
#property indicator_color1  clrLime,clrRed
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

//--- input parameters
input int      InpPeriod = 10;        // ATR Period
input double   InpMultiplier = 3.0;   // Multiplier

//--- indicator buffers
double SupertrendBuffer[];
double ColorBuffer[];
double ATRBuffer[];

//--- global variables
int atr_handle;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- indicator buffers mapping
    SetIndexBuffer(0, SupertrendBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, ColorBuffer, INDICATOR_COLOR_INDEX);
    SetIndexBuffer(2, ATRBuffer, INDICATOR_CALCULATIONS);
    
    //--- set indicator properties
    IndicatorSetString(INDICATOR_SHORTNAME, "Supertrend(" + IntegerToString(InpPeriod) + "," + DoubleToString(InpMultiplier, 1) + ")");
    IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
    
    //--- create ATR handle
    atr_handle = iATR(_Symbol, PERIOD_CURRENT, InpPeriod);
    if(atr_handle == INVALID_HANDLE)
    {
        Print("Failed to create ATR indicator");
        return INIT_FAILED;
    }
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    //--- check for minimum bars
    if(rates_total < InpPeriod)
        return 0;
        
    //--- get ATR values
    if(CopyBuffer(atr_handle, 0, 0, rates_total, ATRBuffer) <= 0)
        return 0;
    
    //--- calculate start position
    int start = prev_calculated;
    if(start < InpPeriod)
        start = InpPeriod;
        
    //--- main calculation loop
    for(int i = start; i < rates_total; i++)
    {
        double hl2 = (high[i] + low[i]) / 2.0;
        double upper_band = hl2 + (InpMultiplier * ATRBuffer[i]);
        double lower_band = hl2 - (InpMultiplier * ATRBuffer[i]);
        
        //--- determine trend direction
        if(i > 0)
        {
            // Upper band calculation
            if(upper_band < SupertrendBuffer[i-1] || close[i-1] > SupertrendBuffer[i-1])
                upper_band = upper_band;
            else
                upper_band = SupertrendBuffer[i-1];
                
            // Lower band calculation  
            if(lower_band > SupertrendBuffer[i-1] || close[i-1] < SupertrendBuffer[i-1])
                lower_band = lower_band;
            else
                lower_band = SupertrendBuffer[i-1];
                
            // Supertrend calculation
            if(SupertrendBuffer[i-1] == upper_band && close[i] <= upper_band)
                SupertrendBuffer[i] = upper_band;
            else if(SupertrendBuffer[i-1] == upper_band && close[i] > upper_band)
                SupertrendBuffer[i] = lower_band;
            else if(SupertrendBuffer[i-1] == lower_band && close[i] >= lower_band)
                SupertrendBuffer[i] = lower_band;
            else if(SupertrendBuffer[i-1] == lower_band && close[i] < lower_band)
                SupertrendBuffer[i] = upper_band;
            else
                SupertrendBuffer[i] = SupertrendBuffer[i-1];
        }
        else
        {
            SupertrendBuffer[i] = lower_band;
        }
        
        //--- set color
        if(close[i] > SupertrendBuffer[i])
            ColorBuffer[i] = 0; // Green (bullish)
        else
            ColorBuffer[i] = 1; // Red (bearish)
    }
    
    return rates_total;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(atr_handle != INVALID_HANDLE)
        IndicatorRelease(atr_handle);
}

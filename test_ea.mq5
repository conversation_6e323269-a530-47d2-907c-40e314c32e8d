//+------------------------------------------------------------------+
//|                                                      test_ea.mq5 |
//|                                    Copyright 2025, InstaScan Dev |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, InstaScan Dev"
#property link      ""
#property version   "1.00"
#property description "Test script for SmartScalpBot EA validation"
#property script_show_inputs

//--- Input parameters
input bool TestRiskCalculation = true;     // Test risk calculation
input bool TestSignalGeneration = true;    // Test signal generation
input bool TestTimeFiltering = true;       // Test time filtering
input bool TestSpreadControl = true;       // Test spread control
input double TestBalance = 100.0;          // Test account balance
input double TestRiskPercent = 2.0;        // Test risk percentage

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("===============================================");
    Print("        SmartScalpBot EA Test Suite");
    Print("===============================================");
    
    if(TestRiskCalculation)
        TestRiskCalculationFunction();
        
    if(TestSignalGeneration)
        TestSignalGenerationFunction();
        
    if(TestTimeFiltering)
        TestTimeFilteringFunction();
        
    if(TestSpreadControl)
        TestSpreadControlFunction();
        
    Print("===============================================");
    Print("           Test Suite Completed");
    Print("===============================================");
}

//+------------------------------------------------------------------+
//| Test risk calculation functionality                              |
//+------------------------------------------------------------------+
void TestRiskCalculationFunction()
{
    Print("--- Testing Risk Calculation ---");
    
    double balance = TestBalance;
    double risk_percent = TestRiskPercent;
    int sl_pips = 10;
    
    double risk_amount = balance * risk_percent / 100.0;
    Print("Balance: $", balance);
    Print("Risk Percent: ", risk_percent, "%");
    Print("Risk Amount: $", risk_amount);
    
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    
    double sl_points = sl_pips * 10;
    double sl_price_diff = sl_points * point;
    
    double lot_size = risk_amount / (sl_price_diff * tick_value / tick_size);
    
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;
    
    Print("Calculated Lot Size: ", lot_size);
    Print("Min Lot: ", min_lot, " | Max Lot: ", max_lot, " | Lot Step: ", lot_step);
    
    if(lot_size >= min_lot && lot_size <= max_lot)
        Print("✓ Risk calculation PASSED");
    else
        Print("✗ Risk calculation FAILED");
        
    Print("");
}

//+------------------------------------------------------------------+
//| Test signal generation functionality                             |
//+------------------------------------------------------------------+
void TestSignalGenerationFunction()
{
    Print("--- Testing Signal Generation ---");
    
    // Test MACD indicator creation
    int macd_handle = iMACD(_Symbol, PERIOD_CURRENT, 12, 26, 9, PRICE_CLOSE);
    if(macd_handle != INVALID_HANDLE)
    {
        Print("✓ MACD indicator created successfully");
        
        double macd_main[], macd_signal[];
        if(CopyBuffer(macd_handle, 0, 0, 2, macd_main) >= 2 &&
           CopyBuffer(macd_handle, 1, 0, 2, macd_signal) >= 2)
        {
            Print("✓ MACD data retrieved successfully");
            Print("MACD Main: ", macd_main[1], " | MACD Signal: ", macd_signal[1]);
            
            bool macd_buy = (macd_main[1] > macd_signal[1] && macd_main[1] > 0 && macd_signal[1] > 0);
            bool macd_sell = (macd_main[1] < macd_signal[1] && macd_main[1] < 0 && macd_signal[1] < 0);
            
            if(macd_buy)
                Print("MACD Signal: BUY");
            else if(macd_sell)
                Print("MACD Signal: SELL");
            else
                Print("MACD Signal: NEUTRAL");
        }
        else
            Print("✗ Failed to retrieve MACD data");
            
        IndicatorRelease(macd_handle);
    }
    else
        Print("✗ Failed to create MACD indicator");
    
    // Test ATR indicator for Supertrend
    int atr_handle = iATR(_Symbol, PERIOD_CURRENT, 10);
    if(atr_handle != INVALID_HANDLE)
    {
        Print("✓ ATR indicator created successfully");
        
        double atr_values[];
        if(CopyBuffer(atr_handle, 0, 0, 3, atr_values) >= 3)
        {
            Print("✓ ATR data retrieved successfully");
            Print("ATR Value: ", atr_values[1]);
        }
        else
            Print("✗ Failed to retrieve ATR data");
            
        IndicatorRelease(atr_handle);
    }
    else
        Print("✗ Failed to create ATR indicator");
        
    Print("");
}

//+------------------------------------------------------------------+
//| Test time filtering functionality                                |
//+------------------------------------------------------------------+
void TestTimeFilteringFunction()
{
    Print("--- Testing Time Filtering ---");
    
    MqlDateTime time_struct;
    TimeToStruct(TimeCurrent(), time_struct);
    
    int current_hour = time_struct.hour;
    int current_minute = time_struct.min;
    int current_time_minutes = current_hour * 60 + current_minute;
    
    Print("Current Time: ", IntegerToString(current_hour, 2, '0'), ":", IntegerToString(current_minute, 2, '0'));
    
    // Test with default session (08:00 - 18:00)
    int start_minutes = 8 * 60;  // 08:00
    int end_minutes = 18 * 60;   // 18:00
    
    bool within_session = (current_time_minutes >= start_minutes && current_time_minutes <= end_minutes);
    
    Print("Trading Session: 08:00 - 18:00");
    Print("Within Session: ", within_session ? "YES" : "NO");
    
    if(within_session)
        Print("✓ Time filtering allows trading");
    else
        Print("✓ Time filtering blocks trading (outside session)");
        
    Print("");
}

//+------------------------------------------------------------------+
//| Test spread control functionality                                |
//+------------------------------------------------------------------+
void TestSpreadControlFunction()
{
    Print("--- Testing Spread Control ---");
    
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double spread_price = ask - bid;
    double spread_points = spread_price / _Point;
    
    Print("Ask: ", ask, " | Bid: ", bid);
    Print("Spread: ", spread_points, " points");
    
    int max_spread = 20; // Default max spread
    bool spread_ok = (spread_points <= max_spread);
    
    Print("Max Allowed Spread: ", max_spread, " points");
    Print("Spread Acceptable: ", spread_ok ? "YES" : "NO");
    
    if(spread_ok)
        Print("✓ Spread control allows trading");
    else
        Print("✓ Spread control blocks trading (spread too high)");
        
    Print("");
}

//+------------------------------------------------------------------+
//| Test utility functions                                           |
//+------------------------------------------------------------------+
void TestUtilityFunctions()
{
    Print("--- Testing Utility Functions ---");
    
    // Test symbol information
    Print("Symbol: ", _Symbol);
    Print("Digits: ", _Digits);
    Print("Point: ", _Point);
    Print("Min Lot: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN));
    Print("Max Lot: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX));
    Print("Lot Step: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP));
    Print("Tick Value: ", SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE));
    Print("Tick Size: ", SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE));
    
    Print("");
}

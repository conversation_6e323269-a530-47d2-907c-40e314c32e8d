===============================================================================
                            SMARTSCALPBOT EA v1.0
                        Professional Scalping Expert Advisor
                           Copyright 2025, InstaScan Dev
===============================================================================

OVERVIEW:
---------
SmartScalpBot is a professional scalping Expert Advisor designed for small 
Forex accounts ($50-$500). It uses advanced risk management, multiple take-profit
levels, trailing stops, and strict daily profit/loss limits to grow accounts
safely and consistently.

KEY FEATURES:
-------------
✓ Smart entry signals using MACD + Supertrend filters
✓ 3-level take profit system (TP1: 10 pips, TP2: 20 pips, TP3: 30 pips)
✓ Intelligent trailing stop management
✓ Risk-based position sizing (default 2% risk per trade)
✓ Daily profit target and loss limit protection
✓ Trading session time filtering
✓ Spread control for optimal execution
✓ Real-time dashboard with statistics
✓ Maximum 1 position open at a time
✓ Maximum 5 trades per day limit

INSTALLATION:
-------------
1. Copy SmartScalpBot.mq5 to: MetaTrader5/MQL5/Experts/
2. Copy SmartScalpBot.set to: MetaTrader5/MQL5/Presets/
3. Open MetaEditor (F4 in MT5)
4. Open SmartScalpBot.mq5 and compile (F7)
5. Restart MetaTrader 5

SETUP FOR LIVE TRADING:
-----------------------
1. Open a chart (recommended: XAUUSD, GBPJPY, or BTCUSD on M5 timeframe)
2. Drag SmartScalpBot.ex5 onto the chart
3. Configure parameters (see PARAMETERS section below)
4. Enable "Allow live trading" and "Allow DLL imports"
5. Click OK to start the EA

RECOMMENDED SETTINGS:
--------------------
Account Size: $50 - $500
Timeframe: M5 (5-minute chart)
Symbols: XAUUSD, GBPJPY, BTCUSD, EURUSD
Leverage: 1:100 to 1:500
Broker Type: ECN/STP preferred

PARAMETERS EXPLANATION:
-----------------------

=== Risk Management ===
Risk_Percent (2.0): Percentage of account to risk per trade
Fixed_Lot_Size (0.01): Override risk % with fixed lot (set to 0 to use risk %)
Max_Trades_Per_Day (5): Maximum number of trades allowed per day
Daily_Profit_Target_USD (10.0): EA stops trading when this profit is reached
Daily_Loss_Limit_USD (5.0): EA stops trading when this loss is reached

=== Take Profit & Stop Loss ===
TP1_Pips (10): First take profit level in pips
TP2_Pips (20): Second take profit level in pips
TP3_Pips (30): Final take profit level in pips
SL_Pips (10): Stop loss distance in pips
Use_Trailing_Stop (true): Enable intelligent trailing stop system

=== Trading Session ===
Start_Hour (08:00): Trading session start time (server time)
End_Hour (18:00): Trading session end time (server time)
Max_Spread_Points (20): Maximum spread allowed for trade entry

=== Indicators ===
Use_MACD_Filter (true): Enable MACD confirmation filter
Use_Supertrend_Filter (true): Enable Supertrend trend filter
MACD_Fast (12): MACD fast EMA period
MACD_Slow (26): MACD slow EMA period
MACD_Signal (9): MACD signal line period
Supertrend_Period (10): Supertrend calculation period
Supertrend_Multiplier (3.0): Supertrend sensitivity multiplier

=== Dashboard ===
Show_Dashboard (true): Display real-time statistics on chart
Dashboard_X (20): Dashboard horizontal position
Dashboard_Y (50): Dashboard vertical position

TRADING STRATEGY:
-----------------
BUY CONDITIONS:
- MACD line above signal line and both above zero
- Price above Supertrend lower band (bullish trend)
- Spread below maximum allowed
- Within trading session hours
- No existing open positions

SELL CONDITIONS:
- MACD line below signal line and both below zero
- Price below Supertrend upper band (bearish trend)
- Spread below maximum allowed
- Within trading session hours
- No existing open positions

TRADE MANAGEMENT:
-----------------
1. Entry: Position opened with TP1 as initial target
2. TP1 Hit: Stop loss moved to breakeven, target changed to TP2
3. TP2 Hit: Trailing stop activated (10 pips), target changed to TP3
4. TP3 Hit: Position closed completely
5. Trailing: Stop loss trails price by 10 pips when in profit

BACKTESTING:
------------
1. Open Strategy Tester (Ctrl+R)
2. Select SmartScalpBot.ex5
3. Load SmartScalpBot.set preset
4. Set date range (recommend last 3 months)
5. Set initial deposit ($50-$100)
6. Run backtest

OPTIMIZATION TIPS:
------------------
- Test different TP/SL combinations for your broker
- Adjust MACD parameters for different market conditions
- Modify trading session hours based on your timezone
- Consider different risk percentages based on account size
- Test on demo account for at least 1 week before going live

RISK WARNINGS:
--------------
⚠️ Forex trading involves substantial risk of loss
⚠️ Past performance does not guarantee future results
⚠️ Never risk more than you can afford to lose
⚠️ Always test on demo account first
⚠️ Monitor EA performance regularly
⚠️ Ensure stable internet connection for live trading

SUPPORT:
--------
For questions, issues, or optimization requests, please contact:
Email: <EMAIL>
Version: 1.0
Last Updated: 2025-01-26

CHANGELOG:
----------
v1.0 (2025-01-26):
- Initial release
- MACD + Supertrend signal system
- 3-level take profit management
- Trailing stop functionality
- Daily profit/loss limits
- Real-time dashboard
- Risk-based position sizing

===============================================================================
                        HAPPY TRADING WITH SMARTSCALPBOT!
===============================================================================

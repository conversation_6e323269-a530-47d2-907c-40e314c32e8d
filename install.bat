@echo off
echo ===============================================================================
echo                        SMARTSCALPBOT EA INSTALLER
echo                           InstaScan Development
echo ===============================================================================
echo.

REM Get MT5 installation path
set "MT5_PATH=%APPDATA%\MetaQuotes\Terminal"

REM Check if MT5 directory exists
if not exist "%MT5_PATH%" (
    echo ERROR: MetaTrader 5 installation not found!
    echo Please make sure MetaTrader 5 is installed and has been run at least once.
    echo.
    pause
    exit /b 1
)

echo Searching for MetaTrader 5 data folder...

REM Find the correct terminal folder (usually has a long hash name)
for /d %%i in ("%MT5_PATH%\*") do (
    if exist "%%i\MQL5" (
        set "DATA_PATH=%%i"
        goto :found
    )
)

echo ERROR: Could not find MT5 data folder!
echo Please ensure MetaTrader 5 has been run at least once.
pause
exit /b 1

:found
echo Found MT5 data folder: %DATA_PATH%
echo.

REM Create directories if they don't exist
if not exist "%DATA_PATH%\MQL5\Experts" mkdir "%DATA_PATH%\MQL5\Experts"
if not exist "%DATA_PATH%\MQL5\Presets" mkdir "%DATA_PATH%\MQL5\Presets"

REM Copy files
echo Installing SmartScalpBot files...
copy "SmartScalpBot.mq5" "%DATA_PATH%\MQL5\Experts\" >nul
copy "SmartScalpBot.set" "%DATA_PATH%\MQL5\Presets\" >nul

echo.
echo ===============================================================================
echo                            INSTALLATION COMPLETE!
echo ===============================================================================
echo.
echo Files installed to:
echo - Expert Advisor: %DATA_PATH%\MQL5\Experts\SmartScalpBot.mq5
echo - Preset File: %DATA_PATH%\MQL5\Presets\SmartScalpBot.set
echo.
echo NEXT STEPS:
echo 1. Open MetaTrader 5
echo 2. Press F4 to open MetaEditor
echo 3. Open SmartScalpBot.mq5 and press F7 to compile
echo 4. Restart MetaTrader 5
echo 5. Drag SmartScalpBot.ex5 onto your chart
echo.
echo For detailed instructions, please read README.txt
echo.
pause

//+------------------------------------------------------------------+
//|                                                SmartScalpBot.mq5 |
//|                                    Copyright 2025, InstaScan Dev |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, InstaScan Dev"
#property link      ""
#property version   "1.00"
#property description "Smart Scalping EA for small accounts with risk management"

//--- Input parameters
input group "=== Risk Management ==="
input double   Risk_Percent = 2.0;              // Risk % per trade
input double   Fixed_Lot_Size = 0.01;           // Fixed lot size (overrides risk % if > 0)
input int      Max_Trades_Per_Day = 5;          // Maximum trades per day
input double   Daily_Profit_Target_USD = 10.0;  // Daily profit target in USD
input double   Daily_Loss_Limit_USD = 5.0;      // Daily loss limit in USD

input group "=== Take Profit & Stop Loss ==="
input int      TP1_Pips = 10;                   // First TP level in pips
input int      TP2_Pips = 20;                   // Second TP level in pips  
input int      TP3_Pips = 30;                   // Final TP level in pips
input int      SL_Pips = 10;                    // Stop Loss in pips
input bool     Use_Trailing_Stop = true;        // Enable trailing stop

input group "=== Trading Session ==="
input string   Start_Hour = "08:00";            // Session start time
input string   End_Hour = "18:00";              // Session end time
input int      Max_Spread_Points = 20;          // Maximum spread allowed

input group "=== Indicators ==="
input bool     Use_MACD_Filter = true;          // Use MACD confirmation
input bool     Use_Supertrend_Filter = true;    // Use Supertrend filter
input int      MACD_Fast = 12;                  // MACD Fast EMA
input int      MACD_Slow = 26;                  // MACD Slow EMA
input int      MACD_Signal = 9;                 // MACD Signal SMA
input int      Supertrend_Period = 10;          // Supertrend period
input double   Supertrend_Multiplier = 3.0;     // Supertrend multiplier

input group "=== Dashboard ==="
input bool     Show_Dashboard = true;           // Show info dashboard
input int      Dashboard_X = 20;                // Dashboard X position
input int      Dashboard_Y = 50;                // Dashboard Y position

//--- Global variables
int macd_handle = INVALID_HANDLE;
int supertrend_handle = INVALID_HANDLE;
double daily_profit = 0.0;
int daily_trades = 0;
int daily_wins = 0;
int daily_losses = 0;
bool ea_disabled_today = false;
datetime last_trade_date = 0;
datetime current_date = 0;

//--- Trade management
#include <Trade\Trade.mqh>
CTrade trade;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize indicators
    if(Use_MACD_Filter)
    {
        macd_handle = iMACD(_Symbol, PERIOD_CURRENT, MACD_Fast, MACD_Slow, MACD_Signal, PRICE_CLOSE);
        if(macd_handle == INVALID_HANDLE)
        {
            Print("Failed to create MACD indicator");
            return INIT_FAILED;
        }
    }
    
    if(Use_Supertrend_Filter)
    {
        // Note: Supertrend is a custom indicator, using ATR + EMA as alternative
        supertrend_handle = iATR(_Symbol, PERIOD_CURRENT, Supertrend_Period);
        if(supertrend_handle == INVALID_HANDLE)
        {
            Print("Failed to create ATR indicator for Supertrend calculation");
            return INIT_FAILED;
        }
    }
    
    // Initialize trade object
    trade.SetExpertMagicNumber(123456);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_FOK);
    
    // Reset daily statistics
    ResetDailyStats();
    
    Print("SmartScalpBot initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    if(macd_handle != INVALID_HANDLE)
        IndicatorRelease(macd_handle);
    if(supertrend_handle != INVALID_HANDLE)
        IndicatorRelease(supertrend_handle);
        
    // Remove dashboard objects
    RemoveDashboard();
    
    Print("SmartScalpBot deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new day started
    CheckNewDay();
    
    // Update daily profit
    UpdateDailyProfit();
    
    // Check daily limits
    if(CheckDailyLimits())
        return;
        
    // Check trading session
    if(!IsWithinTradingSession())
        return;
        
    // Check spread
    if(!IsSpreadAcceptable())
        return;
        
    // Check if we can open new trades
    if(!CanOpenNewTrade())
        return;
        
    // Get trading signals
    int signal = GetTradingSignal();
    
    // Execute trades based on signal
    if(signal == 1) // Buy signal
        OpenBuyTrade();
    else if(signal == -1) // Sell signal
        OpenSellTrade();
        
    // Manage existing trades
    ManageOpenTrades();
    
    // Update dashboard
    if(Show_Dashboard)
        UpdateDashboard();
}

//+------------------------------------------------------------------+
//| Check if new day started and reset statistics                   |
//+------------------------------------------------------------------+
void CheckNewDay()
{
    datetime today = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    
    if(current_date != today)
    {
        current_date = today;
        ResetDailyStats();
        ea_disabled_today = false;
        Print("New trading day started - statistics reset");
    }
}

//+------------------------------------------------------------------+
//| Reset daily statistics                                           |
//+------------------------------------------------------------------+
void ResetDailyStats()
{
    daily_profit = 0.0;
    daily_trades = 0;
    daily_wins = 0;
    daily_losses = 0;
}

//+------------------------------------------------------------------+
//| Update daily profit from closed positions                       |
//+------------------------------------------------------------------+
void UpdateDailyProfit()
{
    daily_profit = 0.0;
    daily_trades = 0;
    daily_wins = 0;
    daily_losses = 0;
    
    // Get today's deals
    datetime today_start = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    datetime today_end = today_start + 86400; // +24 hours
    
    if(!HistorySelect(today_start, today_end))
        return;
        
    int total_deals = HistoryDealsTotal();
    
    for(int i = 0; i < total_deals; i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket == 0) continue;
        
        if(HistoryDealGetString(ticket, DEAL_SYMBOL) != _Symbol) continue;
        if(HistoryDealGetInteger(ticket, DEAL_MAGIC) != 123456) continue;
        if(HistoryDealGetInteger(ticket, DEAL_TYPE) != DEAL_TYPE_BUY && 
           HistoryDealGetInteger(ticket, DEAL_TYPE) != DEAL_TYPE_SELL) continue;
           
        double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
        daily_profit += profit;
        
        if(profit > 0)
            daily_wins++;
        else if(profit < 0)
            daily_losses++;
            
        daily_trades++;
    }
}

//+------------------------------------------------------------------+
//| Check daily profit/loss limits                                  |
//+------------------------------------------------------------------+
bool CheckDailyLimits()
{
    if(ea_disabled_today)
        return true;

    // Check profit target
    if(daily_profit >= Daily_Profit_Target_USD)
    {
        ea_disabled_today = true;
        Print("Daily profit target reached: $", daily_profit, " - EA disabled for today");
        return true;
    }

    // Check loss limit
    if(daily_profit <= -Daily_Loss_Limit_USD)
    {
        ea_disabled_today = true;
        Print("Daily loss limit reached: $", daily_profit, " - EA disabled for today");
        return true;
    }

    // Check max trades per day
    if(daily_trades >= Max_Trades_Per_Day)
    {
        ea_disabled_today = true;
        Print("Maximum trades per day reached: ", daily_trades, " - EA disabled for today");
        return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| Check if within trading session                                 |
//+------------------------------------------------------------------+
bool IsWithinTradingSession()
{
    MqlDateTime time_struct;
    TimeToStruct(TimeCurrent(), time_struct);

    int current_hour = time_struct.hour;
    int current_minute = time_struct.min;
    int current_time_minutes = current_hour * 60 + current_minute;

    // Parse start and end times
    string start_parts[];
    string end_parts[];
    StringSplit(Start_Hour, ':', start_parts);
    StringSplit(End_Hour, ':', end_parts);

    int start_minutes = (int)StringToInteger(start_parts[0]) * 60 + (int)StringToInteger(start_parts[1]);
    int end_minutes = (int)StringToInteger(end_parts[0]) * 60 + (int)StringToInteger(end_parts[1]);

    return (current_time_minutes >= start_minutes && current_time_minutes <= end_minutes);
}

//+------------------------------------------------------------------+
//| Check if spread is acceptable                                   |
//+------------------------------------------------------------------+
bool IsSpreadAcceptable()
{
    double spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    return (spread <= Max_Spread_Points);
}

//+------------------------------------------------------------------+
//| Check if we can open new trade                                  |
//+------------------------------------------------------------------+
bool CanOpenNewTrade()
{
    // Check if we already have open positions
    int total_positions = PositionsTotal();
    for(int i = 0; i < total_positions; i++)
    {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == 123456)
            return false; // Already have a position
    }

    return true;
}

//+------------------------------------------------------------------+
//| Get trading signal based on indicators                          |
//+------------------------------------------------------------------+
int GetTradingSignal()
{
    double macd_main[], macd_signal[];
    double atr_values[];
    double close_prices[];

    // Get current prices
    if(CopyClose(_Symbol, PERIOD_CURRENT, 0, 3, close_prices) < 3)
        return 0;

    bool buy_signal = true;
    bool sell_signal = true;

    // MACD Filter
    if(Use_MACD_Filter && macd_handle != INVALID_HANDLE)
    {
        if(CopyBuffer(macd_handle, 0, 0, 2, macd_main) < 2 ||
           CopyBuffer(macd_handle, 1, 0, 2, macd_signal) < 2)
            return 0;

        // Buy: MACD line above signal and both above 0
        bool macd_buy = (macd_main[1] > macd_signal[1] && macd_main[1] > 0 && macd_signal[1] > 0);
        // Sell: MACD line below signal and both below 0
        bool macd_sell = (macd_main[1] < macd_signal[1] && macd_main[1] < 0 && macd_signal[1] < 0);

        buy_signal = buy_signal && macd_buy;
        sell_signal = sell_signal && macd_sell;
    }

    // Supertrend Filter (simplified using ATR and price action)
    if(Use_Supertrend_Filter && supertrend_handle != INVALID_HANDLE)
    {
        if(CopyBuffer(supertrend_handle, 0, 0, 3, atr_values) < 3)
            return 0;

        // Simplified Supertrend logic
        double hl2 = (SymbolInfoDouble(_Symbol, SYMBOL_LASTHIGH) + SymbolInfoDouble(_Symbol, SYMBOL_LASTLOW)) / 2;
        double upper_band = hl2 + (Supertrend_Multiplier * atr_values[1]);
        double lower_band = hl2 - (Supertrend_Multiplier * atr_values[1]);

        double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

        // Buy: Price above lower band (bullish trend)
        bool supertrend_buy = (current_price > lower_band);
        // Sell: Price below upper band (bearish trend)
        bool supertrend_sell = (current_price < upper_band);

        buy_signal = buy_signal && supertrend_buy;
        sell_signal = sell_signal && supertrend_sell;
    }

    if(buy_signal && !sell_signal)
        return 1;  // Buy signal
    else if(sell_signal && !buy_signal)
        return -1; // Sell signal
    else
        return 0;  // No signal
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk                                |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
    if(Fixed_Lot_Size > 0)
        return Fixed_Lot_Size;

    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = balance * Risk_Percent / 100.0;

    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    double sl_points = SL_Pips * 10; // Convert pips to points
    double sl_price_diff = sl_points * point;

    double lot_size = risk_amount / (sl_price_diff * tick_value / tick_size);

    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

    return lot_size;
}

//+------------------------------------------------------------------+
//| Open Buy Trade                                                  |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double lot_size = CalculateLotSize();

    double sl = ask - (SL_Pips * 10 * _Point);
    double tp1 = ask + (TP1_Pips * 10 * _Point);
    double tp2 = ask + (TP2_Pips * 10 * _Point);
    double tp3 = ask + (TP3_Pips * 10 * _Point);

    // Open position with TP1 initially
    if(trade.Buy(lot_size, _Symbol, ask, sl, tp1, "SmartScalp BUY"))
    {
        Print("Buy trade opened: Lot=", lot_size, " Entry=", ask, " SL=", sl, " TP=", tp1);

        // Store TP levels in global variables for management
        ulong ticket = trade.ResultOrder();
        GlobalVariableSet("TP1_" + IntegerToString(ticket), tp1);
        GlobalVariableSet("TP2_" + IntegerToString(ticket), tp2);
        GlobalVariableSet("TP3_" + IntegerToString(ticket), tp3);
        GlobalVariableSet("TP_Level_" + IntegerToString(ticket), 1);
    }
    else
    {
        Print("Failed to open buy trade: ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Open Sell Trade                                                 |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double lot_size = CalculateLotSize();

    double sl = bid + (SL_Pips * 10 * _Point);
    double tp1 = bid - (TP1_Pips * 10 * _Point);
    double tp2 = bid - (TP2_Pips * 10 * _Point);
    double tp3 = bid - (TP3_Pips * 10 * _Point);

    // Open position with TP1 initially
    if(trade.Sell(lot_size, _Symbol, bid, sl, tp1, "SmartScalp SELL"))
    {
        Print("Sell trade opened: Lot=", lot_size, " Entry=", bid, " SL=", sl, " TP=", tp1);

        // Store TP levels in global variables for management
        ulong ticket = trade.ResultOrder();
        GlobalVariableSet("TP1_" + IntegerToString(ticket), tp1);
        GlobalVariableSet("TP2_" + IntegerToString(ticket), tp2);
        GlobalVariableSet("TP3_" + IntegerToString(ticket), tp3);
        GlobalVariableSet("TP_Level_" + IntegerToString(ticket), 1);
    }
    else
    {
        Print("Failed to open sell trade: ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Manage open trades (trailing stop, partial closes)             |
//+------------------------------------------------------------------+
void ManageOpenTrades()
{
    int total_positions = PositionsTotal();

    for(int i = total_positions - 1; i >= 0; i--)
    {
        if(!PositionGetSymbol(i) == _Symbol) continue;
        if(PositionGetInteger(POSITION_MAGIC) != 123456) continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        double current_price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ?
                              SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                              SymbolInfoDouble(_Symbol, SYMBOL_ASK);

        double entry_price = PositionGetDouble(POSITION_PRICE_OPEN);
        double current_sl = PositionGetDouble(POSITION_SL);
        double current_tp = PositionGetDouble(POSITION_TP);

        // Get stored TP levels
        string ticket_str = IntegerToString(ticket);
        double tp1 = GlobalVariableGet("TP1_" + ticket_str);
        double tp2 = GlobalVariableGet("TP2_" + ticket_str);
        double tp3 = GlobalVariableGet("TP3_" + ticket_str);
        int tp_level = (int)GlobalVariableGet("TP_Level_" + ticket_str);

        if(Use_Trailing_Stop)
        {
            ManageTrailingStop(ticket, current_price, entry_price, current_sl, tp1, tp2, tp3, tp_level);
        }
    }
}

//+------------------------------------------------------------------+
//| Manage trailing stop logic                                      |
//+------------------------------------------------------------------+
void ManageTrailingStop(ulong ticket, double current_price, double entry_price,
                       double current_sl, double tp1, double tp2, double tp3, int tp_level)
{
    string ticket_str = IntegerToString(ticket);
    bool is_buy = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY);

    if(is_buy)
    {
        // Buy position management
        if(tp_level == 1 && current_price >= tp1)
        {
            // TP1 hit - move SL to breakeven
            double new_sl = entry_price;
            if(new_sl > current_sl)
            {
                trade.PositionModify(ticket, new_sl, tp2);
                GlobalVariableSet("TP_Level_" + ticket_str, 2);
                Print("TP1 hit - SL moved to breakeven for ticket ", ticket);
            }
        }
        else if(tp_level == 2 && current_price >= tp2)
        {
            // TP2 hit - start trailing by 10 pips
            double trail_distance = 10 * 10 * _Point; // 10 pips in points
            double new_sl = current_price - trail_distance;
            if(new_sl > current_sl)
            {
                trade.PositionModify(ticket, new_sl, tp3);
                GlobalVariableSet("TP_Level_" + ticket_str, 3);
                Print("TP2 hit - Trailing stop activated for ticket ", ticket);
            }
        }
        else if(tp_level == 3)
        {
            // Continue trailing
            double trail_distance = 10 * 10 * _Point;
            double new_sl = current_price - trail_distance;
            if(new_sl > current_sl)
            {
                trade.PositionModify(ticket, new_sl, tp3);
            }

            // Close at TP3
            if(current_price >= tp3)
            {
                trade.PositionClose(ticket);
                CleanupGlobalVariables(ticket_str);
                Print("TP3 hit - Position closed for ticket ", ticket);
            }
        }
    }
    else
    {
        // Sell position management
        if(tp_level == 1 && current_price <= tp1)
        {
            // TP1 hit - move SL to breakeven
            double new_sl = entry_price;
            if(new_sl < current_sl)
            {
                trade.PositionModify(ticket, new_sl, tp2);
                GlobalVariableSet("TP_Level_" + ticket_str, 2);
                Print("TP1 hit - SL moved to breakeven for ticket ", ticket);
            }
        }
        else if(tp_level == 2 && current_price <= tp2)
        {
            // TP2 hit - start trailing by 10 pips
            double trail_distance = 10 * 10 * _Point;
            double new_sl = current_price + trail_distance;
            if(new_sl < current_sl)
            {
                trade.PositionModify(ticket, new_sl, tp3);
                GlobalVariableSet("TP_Level_" + ticket_str, 3);
                Print("TP2 hit - Trailing stop activated for ticket ", ticket);
            }
        }
        else if(tp_level == 3)
        {
            // Continue trailing
            double trail_distance = 10 * 10 * _Point;
            double new_sl = current_price + trail_distance;
            if(new_sl < current_sl)
            {
                trade.PositionModify(ticket, new_sl, tp3);
            }

            // Close at TP3
            if(current_price <= tp3)
            {
                trade.PositionClose(ticket);
                CleanupGlobalVariables(ticket_str);
                Print("TP3 hit - Position closed for ticket ", ticket);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Cleanup global variables for closed position                    |
//+------------------------------------------------------------------+
void CleanupGlobalVariables(string ticket_str)
{
    GlobalVariableDel("TP1_" + ticket_str);
    GlobalVariableDel("TP2_" + ticket_str);
    GlobalVariableDel("TP3_" + ticket_str);
    GlobalVariableDel("TP_Level_" + ticket_str);
}

//+------------------------------------------------------------------+
//| Update dashboard display                                         |
//+------------------------------------------------------------------+
void UpdateDashboard()
{
    string status = ea_disabled_today ? "DISABLED" : "ACTIVE";
    color status_color = ea_disabled_today ? clrRed : clrGreen;

    // Create dashboard text
    string dashboard_text = "=== SmartScalpBot ===\n";
    dashboard_text += "Status: " + status + "\n";
    dashboard_text += "Daily P&L: $" + DoubleToString(daily_profit, 2) + "\n";
    dashboard_text += "Trades: " + IntegerToString(daily_trades) + "/" + IntegerToString(Max_Trades_Per_Day) + "\n";
    dashboard_text += "Wins: " + IntegerToString(daily_wins) + " | Losses: " + IntegerToString(daily_losses) + "\n";
    dashboard_text += "Target: $" + DoubleToString(Daily_Profit_Target_USD, 2) + "\n";
    dashboard_text += "Limit: -$" + DoubleToString(Daily_Loss_Limit_USD, 2) + "\n";

    // Display dashboard
    ObjectCreate(0, "SmartScalp_Dashboard", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, "SmartScalp_Dashboard", OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, "SmartScalp_Dashboard", OBJPROP_XDISTANCE, Dashboard_X);
    ObjectSetInteger(0, "SmartScalp_Dashboard", OBJPROP_YDISTANCE, Dashboard_Y);
    ObjectSetString(0, "SmartScalp_Dashboard", OBJPROP_TEXT, dashboard_text);
    ObjectSetInteger(0, "SmartScalp_Dashboard", OBJPROP_COLOR, status_color);
    ObjectSetInteger(0, "SmartScalp_Dashboard", OBJPROP_FONTSIZE, 9);
    ObjectSetString(0, "SmartScalp_Dashboard", OBJPROP_FONT, "Courier New");
}

//+------------------------------------------------------------------+
//| Remove dashboard objects                                         |
//+------------------------------------------------------------------+
void RemoveDashboard()
{
    ObjectDelete(0, "SmartScalp_Dashboard");
}
